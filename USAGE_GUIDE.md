# 交通事故案例向量化搜索系统使用指南

## 🎉 系统状态：正常运行

基于 `shibing624/text2vec-base-chinese` 模型的向量化搜索系统已成功解决环境兼容性问题，现在可以稳定运行。

## 📋 系统要求

- Python 3.11+ (推荐使用conda环境)
- 已安装的依赖包：
  - sentence-transformers
  - faiss-cpu
  - numpy
  - beautifulsoup4
  - torch
  - transformers

## 🚀 快速开始

### 1. 激活正确的Python环境

```bash
# 确保使用search311环境
conda activate search311

# 或者直接使用完整路径
/opt/anaconda3/envs/search311/bin/python
```

### 2. 构建索引（首次使用）

```bash
# 使用启动脚本构建索引
python start_indexer.py build
```

**注意**：
- 首次构建索引需要1-2小时（处理8758个案例）
- 系统会自动设置环境变量解决OpenMP冲突
- 进度会每100个案例显示一次

### 3. 交互式搜索

```bash
# 索引构建完成后，启动交互式搜索
python start_indexer.py search
```

### 4. 测试系统

```bash
# 构建索引并运行测试
python start_indexer.py test
```

## 💡 使用示例

### 程序化使用

```python
from traffic_accident_indexer import TrafficAccidentIndexer

# 加载已构建的索引
indexer = TrafficAccidentIndexer()
indexer.load_index()

# 执行搜索
results = indexer.search("交通事故责任认定", top_k=5)

# 处理结果
for score, case in results:
    case_number = case['original_data']['s7']
    summary = case['original_data']['s25_summary']
    print(f"相似度: {score:.4f}")
    print(f"案号: {case_number}")
    print(f"摘要: {summary[:100]}...")
    print("-" * 50)
```

### 高级搜索功能

```python
# 带过滤条件的搜索
results = indexer.search_with_filters(
    query="机动车保险赔偿",
    top_k=10,
    case_number_filter="民终",  # 只搜索包含"民终"的案号
    min_similarity=0.3         # 最小相似度阈值
)

# 获取系统统计信息
stats = indexer.get_statistics()
print(f"案例总数: {stats['total_cases']}")
print(f"向量维度: {stats['vector_dimension']}")

# 导出搜索结果
indexer.export_search_results(results, "search_results.json")
```

## 📁 文件结构

```
HMSSimSearch/
├── traffic_accident_cases.json     # 原始数据文件
├── traffic_accident_indexer.py     # 核心索引器
├── start_indexer.py               # 启动脚本
├── requirements.txt               # 依赖包列表
├── README.md                      # 详细文档
├── USAGE_GUIDE.md                # 本使用指南
└── faiss_index/                  # 索引文件目录
    ├── traffic_accident.index    # Faiss索引文件
    ├── case_data.pkl            # 案例数据
    └── config.json              # 配置信息
```

## 🔍 搜索示例

### 常用查询

1. **责任认定相关**
   - "交通事故责任认定"
   - "主要责任次要责任"
   - "无责任认定"

2. **保险赔偿相关**
   - "机动车保险赔偿"
   - "交强险赔偿限额"
   - "商业险理赔"

3. **损害赔偿相关**
   - "人身损害赔偿"
   - "误工费计算标准"
   - "护理费营养费"

4. **法律条文相关**
   - "道路交通安全法"
   - "民法典侵权责任"
   - "保险法条款"

## ⚡ 性能优化

### 当前性能指标
- **处理速度**: 约100个案例/25-30秒
- **搜索速度**: 毫秒级响应
- **内存使用**: 适中（逐个处理避免内存溢出）
- **向量维度**: 768维（text2vec-base-chinese标准）

### 优化建议
1. **首次构建**: 耐心等待索引构建完成
2. **后续使用**: 直接加载已保存的索引，无需重建
3. **内存管理**: 系统已自动优化内存使用
4. **并发搜索**: 支持多个查询并发执行

## 🛠️ 故障排除

### 常见问题

1. **OpenMP冲突错误**
   - 解决方案：启动脚本已自动设置环境变量
   - 手动设置：`export KMP_DUPLICATE_LIB_OK=TRUE`

2. **段错误（Segmentation fault）**
   - 解决方案：使用单个文本处理模式（已实现）
   - 确保使用正确的Python环境

3. **内存不足**
   - 解决方案：系统已优化为逐个处理，定期清理内存

4. **模型下载失败**
   - 检查网络连接
   - 模型会自动缓存到本地

### 环境检查

```bash
# 检查Python环境
python start_indexer.py build

# 如果环境检查失败，查看具体缺少的包
pip list | grep -E "(sentence-transformers|faiss|numpy|beautifulsoup4)"
```

## 📊 系统架构

```
用户查询 → 文本预处理 → 向量化(text2vec-base-chinese) → Faiss搜索 → 结果排序 → 返回结果
    ↓
原始数据 → 文本提取 → 向量化 → 索引构建 → 持久化存储
```

## 🎯 下一步

1. **立即可用**: 系统正在构建索引，完成后即可使用
2. **功能扩展**: 可以基于现有框架添加更多功能
3. **性能调优**: 根据实际使用情况进一步优化
4. **数据更新**: 支持增量更新索引

---

**状态更新**: 系统正在稳定运行中，已成功处理2300+案例，预计1-2小时内完成全部索引构建。
