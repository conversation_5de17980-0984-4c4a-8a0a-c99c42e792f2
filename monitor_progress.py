#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控索引构建进度
"""

import os
import time
import re
from datetime import datetime, timed<PERSON><PERSON>

def parse_log_file(log_file="traffic_accident_indexer.log"):
    """解析日志文件获取进度信息"""
    if not os.path.exists(log_file):
        return None, None, None
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 查找最新的进度信息
        latest_progress = None
        start_time = None
        total_cases = None
        
        for line in lines:
            # 查找总案例数
            if "成功加载" in line and "个有效案例" in line:
                match = re.search(r'成功加载 (\d+) 个有效案例', line)
                if match:
                    total_cases = int(match.group(1))
            
            # 查找开始时间
            if "开始构建向量索引" in line:
                time_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                if time_match:
                    start_time = datetime.strptime(time_match.group(1), '%Y-%m-%d %H:%M:%S')
            
            # 查找最新进度
            if "正在处理案例" in line:
                match = re.search(r'正在处理案例 (\d+)/(\d+)', line)
                if match:
                    current = int(match.group(1))
                    total = int(match.group(2))
                    time_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                    if time_match:
                        current_time = datetime.strptime(time_match.group(1), '%Y-%m-%d %H:%M:%S')
                        latest_progress = (current, total, current_time)
        
        return latest_progress, start_time, total_cases
        
    except Exception as e:
        print(f"解析日志文件失败: {e}")
        return None, None, None

def calculate_eta(current, total, start_time, current_time):
    """计算预计完成时间"""
    if current <= 0 or not start_time:
        return None
    
    elapsed = current_time - start_time
    rate = current / elapsed.total_seconds()  # 案例/秒
    remaining = total - current
    eta_seconds = remaining / rate if rate > 0 else 0
    eta = current_time + timedelta(seconds=eta_seconds)
    
    return eta, elapsed, rate

def format_time_delta(td):
    """格式化时间差"""
    total_seconds = int(td.total_seconds())
    hours = total_seconds // 3600
    minutes = (total_seconds % 3600) // 60
    seconds = total_seconds % 60
    
    if hours > 0:
        return f"{hours}小时{minutes}分钟{seconds}秒"
    elif minutes > 0:
        return f"{minutes}分钟{seconds}秒"
    else:
        return f"{seconds}秒"

def main():
    """主监控函数"""
    print("交通事故案例索引构建进度监控")
    print("=" * 50)
    
    while True:
        try:
            # 解析日志
            progress, start_time, total_cases = parse_log_file()
            
            if progress is None:
                print("未找到进度信息，可能索引构建尚未开始或日志文件不存在")
                print("请确保索引构建程序正在运行...")
            else:
                current, total, current_time = progress
                percentage = (current / total) * 100
                
                print(f"\n当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"进度: {current}/{total} ({percentage:.1f}%)")
                print(f"进度条: {'█' * int(percentage/2):<50} {percentage:.1f}%")
                
                if start_time:
                    eta_info = calculate_eta(current, total, start_time, current_time)
                    if eta_info:
                        eta, elapsed, rate = eta_info
                        print(f"已用时间: {format_time_delta(elapsed)}")
                        print(f"处理速度: {rate*60:.1f} 案例/分钟")
                        print(f"预计完成: {eta.strftime('%Y-%m-%d %H:%M:%S')}")
                        
                        remaining_time = eta - datetime.now()
                        if remaining_time.total_seconds() > 0:
                            print(f"剩余时间: {format_time_delta(remaining_time)}")
                        else:
                            print("即将完成！")
                
                # 检查是否完成
                if current >= total:
                    print("\n🎉 索引构建已完成！")
                    break
            
            print("\n按 Ctrl+C 退出监控")
            print("-" * 50)
            
            # 等待30秒后更新
            time.sleep(30)
            
        except KeyboardInterrupt:
            print("\n\n监控已停止")
            break
        except Exception as e:
            print(f"监控出错: {e}")
            time.sleep(10)

if __name__ == "__main__":
    main()
