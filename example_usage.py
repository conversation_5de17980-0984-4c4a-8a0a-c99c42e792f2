#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交通事故案例搜索系统使用示例

展示系统的各种使用方法和功能
"""

from traffic_accident_indexer import TrafficAccidentIndexer, TrafficAccidentSearchEngine
import json
import time


def example_1_basic_usage():
    """示例1: 基本使用方法"""
    print("=== 示例1: 基本使用方法 ===")
    
    # 创建索引器
    indexer = TrafficAccidentIndexer()
    
    # 加载数据
    print("正在加载数据...")
    indexer.load_data("traffic_accident_cases.json")
    print(f"已加载 {len(indexer.case_data)} 个案例")
    
    # 构建索引
    print("正在构建索引...")
    indexer.build_index(batch_size=16)
    
    # 保存索引
    indexer.save_index("example_index")
    print("索引已保存")
    
    # 执行搜索
    query = "交通事故责任认定"
    print(f"\n搜索查询: '{query}'")
    results = indexer.search(query, top_k=3)
    
    print(f"找到 {len(results)} 个相关案例:")
    for i, (score, case) in enumerate(results, 1):
        case_number = case['original_data'].get('s7', '未知案号')
        summary = case['original_data'].get('s25_summary', '无摘要')[:100]
        print(f"\n{i}. 案号: {case_number}")
        print(f"   相似度: {score:.4f}")
        print(f"   摘要: {summary}...")


def example_2_search_engine():
    """示例2: 使用高级搜索引擎"""
    print("\n=== 示例2: 高级搜索引擎 ===")
    
    # 初始化搜索引擎
    engine = TrafficAccidentSearchEngine("example_index")
    engine.initialize()  # 会自动加载已保存的索引
    
    # 获取系统统计信息
    stats = engine.get_stats()
    print("系统统计信息:")
    print(f"  案例总数: {stats['total_cases']}")
    print(f"  向量维度: {stats['vector_dimension']}")
    print(f"  平均文本长度: {stats['average_text_length']:.0f} 字符")
    
    # 执行多个搜索查询
    queries = [
        "机动车保险赔偿标准",
        "人身损害赔偿计算",
        "交通肇事逃逸处理"
    ]
    
    for query in queries:
        print(f"\n搜索: '{query}'")
        results = engine.search(query, top_k=2, min_similarity=0.1)
        
        if results:
            for i, result in enumerate(results, 1):
                print(f"  {i}. {result['case_number']} (相似度: {result['similarity_score']})")
                print(f"     {result['summary'][:80]}...")
        else:
            print("  未找到相关案例")


def example_3_filtered_search():
    """示例3: 带过滤条件的搜索"""
    print("\n=== 示例3: 过滤搜索 ===")
    
    # 使用已保存的索引
    indexer = TrafficAccidentIndexer()
    indexer.load_index("example_index")
    
    # 基本搜索
    query = "保险赔偿"
    print(f"基本搜索: '{query}'")
    basic_results = indexer.search(query, top_k=5)
    print(f"基本搜索找到 {len(basic_results)} 个结果")
    
    # 带相似度过滤的搜索
    print(f"\n带相似度过滤的搜索 (最小相似度: 0.3)")
    filtered_results = indexer.search_with_filters(
        query, 
        top_k=5, 
        min_similarity=0.3
    )
    print(f"过滤后找到 {len(filtered_results)} 个结果")
    
    # 带案号过滤的搜索
    print(f"\n带案号过滤的搜索 (包含'民终')")
    case_filtered_results = indexer.search_with_filters(
        query,
        top_k=5,
        case_number_filter="民终"
    )
    print(f"案号过滤后找到 {len(case_filtered_results)} 个结果")
    
    # 显示过滤结果
    for i, (score, case) in enumerate(case_filtered_results[:3], 1):
        case_number = case['original_data'].get('s7', '未知案号')
        print(f"  {i}. {case_number} (相似度: {score:.4f})")


def example_4_export_results():
    """示例4: 导出搜索结果"""
    print("\n=== 示例4: 导出搜索结果 ===")
    
    indexer = TrafficAccidentIndexer()
    indexer.load_index("example_index")
    
    # 执行搜索
    query = "误工费计算标准"
    results = indexer.search(query, top_k=5)
    
    # 导出结果
    output_file = "example_search_results.json"
    indexer.export_search_results(results, output_file)
    print(f"搜索结果已导出到: {output_file}")
    
    # 读取并显示导出的结果
    with open(output_file, 'r', encoding='utf-8') as f:
        exported_data = json.load(f)
    
    print(f"\n导出的结果包含 {len(exported_data)} 个案例:")
    for i, item in enumerate(exported_data[:3], 1):
        print(f"  {i}. 案号: {item['case_number']}")
        print(f"     相似度: {item['similarity_score']}")
        print(f"     摘要: {item['summary'][:60]}...")


def example_5_performance_test():
    """示例5: 性能测试"""
    print("\n=== 示例5: 性能测试 ===")
    
    engine = TrafficAccidentSearchEngine("example_index")
    engine.initialize()
    
    # 准备测试查询
    test_queries = [
        "交通事故责任认定",
        "机动车保险理赔",
        "人身损害赔偿",
        "伤残鉴定标准",
        "误工费护理费",
        "交强险赔偿限额",
        "道路交通安全法",
        "车辆挂靠责任"
    ]
    
    print(f"执行 {len(test_queries)} 个查询的性能测试...")
    
    total_time = 0
    all_results = []
    
    for i, query in enumerate(test_queries, 1):
        start_time = time.time()
        results = engine.search(query, top_k=3)
        query_time = time.time() - start_time
        total_time += query_time
        all_results.extend(results)
        
        print(f"  查询 {i}: '{query}' - {query_time:.4f}秒, {len(results)}个结果")
    
    # 性能统计
    avg_time = total_time / len(test_queries)
    print(f"\n性能统计:")
    print(f"  总查询时间: {total_time:.4f} 秒")
    print(f"  平均查询时间: {avg_time:.4f} 秒")
    print(f"  查询吞吐量: {1/avg_time:.2f} 查询/秒")
    print(f"  总结果数: {len(all_results)}")


def example_6_incremental_search():
    """示例6: 渐进式搜索演示"""
    print("\n=== 示例6: 渐进式搜索 ===")
    
    engine = TrafficAccidentSearchEngine("example_index")
    engine.initialize()
    
    base_query = "交通事故"
    
    # 逐步细化搜索
    refined_queries = [
        "交通事故",
        "交通事故责任",
        "交通事故责任认定",
        "交通事故责任认定标准"
    ]
    
    print("演示搜索查询的逐步细化:")
    
    for i, query in enumerate(refined_queries, 1):
        print(f"\n第{i}步: '{query}'")
        results = engine.search(query, top_k=3)
        
        print(f"  找到 {len(results)} 个结果:")
        for j, result in enumerate(results, 1):
            print(f"    {j}. {result['case_number']} (相似度: {result['similarity_score']:.4f})")


def cleanup_examples():
    """清理示例文件"""
    import os
    import shutil
    
    files_to_remove = [
        "example_search_results.json",
        "traffic_accident_indexer.log"
    ]
    
    dirs_to_remove = [
        "example_index"
    ]
    
    print("\n=== 清理示例文件 ===")
    
    for file_path in files_to_remove:
        if os.path.exists(file_path):
            os.remove(file_path)
            print(f"删除文件: {file_path}")
    
    for dir_path in dirs_to_remove:
        if os.path.exists(dir_path):
            shutil.rmtree(dir_path)
            print(f"删除目录: {dir_path}")


def main():
    """运行所有示例"""
    print("交通事故案例搜索系统 - 使用示例")
    print("=" * 50)
    
    try:
        # 运行各个示例
        example_1_basic_usage()
        example_2_search_engine()
        example_3_filtered_search()
        example_4_export_results()
        example_5_performance_test()
        example_6_incremental_search()
        
        print("\n" + "=" * 50)
        print("✅ 所有示例运行完成！")
        
        # 询问是否清理文件
        try:
            response = input("\n是否清理示例生成的文件? (y/n): ").lower().strip()
            if response in ['y', 'yes', '是']:
                cleanup_examples()
                print("清理完成")
        except KeyboardInterrupt:
            print("\n示例结束")
            
    except Exception as e:
        print(f"\n❌ 示例运行失败: {e}")
        print("请确保 traffic_accident_cases.json 文件存在于当前目录")


if __name__ == "__main__":
    main()
