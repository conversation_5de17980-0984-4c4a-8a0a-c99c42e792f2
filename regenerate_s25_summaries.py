#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强制重新生成交通事故案例的s25_summary字段

功能：
1. 读取traffic_accident_cases.json文件
2. 使用BART模型强制重新生成所有案例的摘要
3. 覆盖现有的s25_summary字段
4. 创建备份并保存更新后的数据

作者: AI Assistant
创建时间: 2025-01-27
"""

import json
import logging
import os
import shutil
import sys
from datetime import datetime
from typing import Dict, Any, List
import time

# 添加当前目录到Python路径，以便导入dynamic_bart_summarizer
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from bart_only_summarizer import BartOnlySummarizer
except ImportError as e:
    print(f"❌ 无法导入BartOnlySummarizer: {e}")
    print("请确保bart_only_summarizer.py文件存在且可用")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('summary_regeneration.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TrafficAccidentSummaryRegenerator:
    def __init__(self, data_file: str = "traffic_accident_cases.json"):
        """
        初始化摘要重新生成器
        
        Args:
            data_file: 数据文件路径
        """
        self.data_file = data_file
        self.data = []
        self.summarizer = None
        self.stats = {
            'total_processed': 0,
            'successful': 0,
            'failed': 0,
            'start_time': None,
            'end_time': None
        }
    
    def load_data(self) -> bool:
        """加载数据文件"""
        try:
            logger.info(f"正在加载数据文件: {self.data_file}")
            
            if not os.path.exists(self.data_file):
                logger.error(f"数据文件不存在: {self.data_file}")
                return False
            
            with open(self.data_file, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            
            logger.info(f"✅ 成功加载 {len(self.data)} 个案例")
            return True
            
        except Exception as e:
            logger.error(f"❌ 加载数据文件失败: {e}")
            return False
    
    def initialize_summarizer(self):
        """初始化纯BART摘要模型"""
        try:
            logger.info("正在初始化纯BART摘要模型...")
            self.summarizer = BartOnlySummarizer("fnlp/bart-base-chinese")
            logger.info("✅ 纯BART摘要模型初始化完成")
        except Exception as e:
            logger.error(f"❌ 纯BART摘要模型初始化失败: {e}")
            raise
    
    def clean_text(self, text: str) -> str:
        """清理文本内容"""
        if not text:
            return ""
        
        import re
        # 去除HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        # 去除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        # 去除特殊字符（保留中文、英文、数字、基本标点）
        text = re.sub(r'[^\u4e00-\u9fff\w\s，。！？；：""''（）【】《》、]', '', text)
        
        return text.strip()
    
    def regenerate_summary_for_case(self, case: Dict[str, Any], case_index: int) -> bool:
        """
        为单个案例强制重新生成摘要
        
        Args:
            case: 案例数据
            case_index: 案例索引
            
        Returns:
            是否成功生成摘要
        """
        try:
            # 检查s25字段
            s25_content = case.get('s25', '')
            if not s25_content or not s25_content.strip():
                logger.warning(f"案例 {case_index}: s25字段为空，跳过")
                return False
            
            # 清理文本
            cleaned_text = self.clean_text(s25_content)
            if len(cleaned_text) < 50:  # 文本太短，不需要摘要
                case['s25_summary'] = cleaned_text
                return True
            
            # 强制重新生成摘要 - 只使用BART模型
            result = self.summarizer.summarize(
                cleaned_text,
                custom_ratio=0.2,  # 20%压缩比例
                verbose=False
            )

            # 纯BART模型只会返回bart_model或抛出异常
            # 保存摘要
            case['s25_summary'] = result['summary']

            # 记录统计信息
            logger.debug(f"案例 {case_index}: BART摘要生成成功 "
                        f"({result['original_length']} → {result['summary_length']} 字符, "
                        f"压缩率: {result['compression_ratio']:.1%})")

            return True
            
        except Exception as e:
            logger.error(f"案例 {case_index}: 摘要生成失败 - {e}")
            return False
    
    def regenerate_all_summaries(self):
        """重新生成所有案例的摘要"""
        logger.info("开始强制重新生成所有摘要...")
        
        self.stats['start_time'] = time.time()
        total_cases = len(self.data)
        
        for i, case in enumerate(self.data):
            self.stats['total_processed'] += 1
            
            # 显示进度
            if (i + 1) % 10 == 0 or i == 0:
                progress = (i + 1) / total_cases * 100
                elapsed_time = time.time() - self.stats['start_time']
                estimated_total_time = elapsed_time / (i + 1) * total_cases
                remaining_time = estimated_total_time - elapsed_time
                
                logger.info(f"进度: {i + 1}/{total_cases} ({progress:.1f}%) - "
                           f"预计剩余时间: {remaining_time/60:.1f}分钟")
            
            # 重新生成摘要
            if self.regenerate_summary_for_case(case, i):
                self.stats['successful'] += 1
            else:
                self.stats['failed'] += 1
            
            # 每100个案例保存一次
            if (i + 1) % 100 == 0:
                logger.info("正在保存更新后的数据...")
                self.save_data()
                logger.info(f"中间保存完成 (已处理 {i + 1} 个案例)")
        
        self.stats['end_time'] = time.time()
        
        # 计算总耗时
        total_time = self.stats['end_time'] - self.stats['start_time']
        logger.info(f"摘要重新生成完成！总耗时: {total_time/60:.1f}分钟")
        logger.info(f"统计: 成功={self.stats['successful']}, 失败={self.stats['failed']}")
    
    def create_backup(self):
        """创建数据文件备份"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = f"traffic_accident_cases_backup_{timestamp}.json"
            shutil.copy2(self.data_file, backup_file)
            logger.info(f"✅ 备份文件已创建: {backup_file}")
            return backup_file
        except Exception as e:
            logger.error(f"❌ 创建备份失败: {e}")
            return None
    
    def save_data(self):
        """保存更新后的数据"""
        try:
            logger.info("正在保存更新后的数据...")
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.data, f, ensure_ascii=False, indent=2)
            logger.info(f"✅ 数据已保存到: {self.data_file}")
        except Exception as e:
            logger.error(f"❌ 保存数据失败: {e}")
            raise
    
    def get_final_statistics(self) -> Dict[str, Any]:
        """获取最终统计信息"""
        total_cases = len(self.data)
        cases_with_summary = sum(1 for case in self.data 
                                if case.get('s25_summary', '').strip())
        
        return {
            'total_cases': total_cases,
            'cases_with_summary': cases_with_summary,
            'summary_coverage': cases_with_summary / total_cases * 100 if total_cases > 0 else 0,
            'processing_stats': self.stats
        }


def main():
    """主函数"""
    print("=" * 60)
    print("交通事故案例摘要强制重新生成器")
    print("使用fnlp/bart-base-chinese模型重新生成所有s25_summary字段")
    print("=" * 60)
    
    # 创建生成器实例
    regenerator = TrafficAccidentSummaryRegenerator()
    
    # 加载数据
    if not regenerator.load_data():
        return
    
    # 显示初始统计信息
    initial_stats = regenerator.get_final_statistics()
    logger.info(f"初始统计: 总案例={initial_stats['total_cases']}, "
               f"已有摘要={initial_stats['cases_with_summary']}")
    
    # 初始化摘要模型
    regenerator.initialize_summarizer()
    
    # 询问用户是否继续
    print(f"\n将强制重新生成 {initial_stats['total_cases']} 个案例的摘要，这可能需要较长时间。是否继续？(y/n): ", end="")
    user_input = input().strip().lower()
    
    if user_input != 'y':
        print("操作已取消")
        return
    
    # 创建备份
    backup_file = regenerator.create_backup()
    if not backup_file:
        print("❌ 无法创建备份文件，操作终止")
        return
    
    try:
        # 重新生成所有摘要
        regenerator.regenerate_all_summaries()
        
        # 保存最终结果
        regenerator.save_data()
        
        # 显示最终统计
        final_stats = regenerator.get_final_statistics()
        logger.info(f"最终统计: 摘要覆盖率={final_stats['summary_coverage']:.1f}%")
        
        print("=" * 60)
        print("✅ 摘要强制重新生成完成！")
        print(f"📊 处理统计:")
        print(f"   - 总处理: {regenerator.stats['total_processed']}")
        print(f"   - 成功: {regenerator.stats['successful']}")
        print(f"   - 失败: {regenerator.stats['failed']}")
        print(f"📁 备份文件: {backup_file}")
        print("=" * 60)
        
    except Exception as e:
        logger.error(f"❌ 处理过程中发生错误: {e}")
        print(f"❌ 处理失败: {e}")
        if backup_file:
            print(f"可以从备份文件恢复: {backup_file}")


if __name__ == "__main__":
    main()
