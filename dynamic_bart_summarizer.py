#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于fnlp/bart-base-chinese的中文摘要生成器
使用BART模型生成高质量的中文摘要
"""

import re
import logging
from typing import Dict, Any, List
import warnings

# 忽略警告
warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings("ignore", category=UserWarning)

try:
    import torch
    from transformers import BartForConditionalGeneration, BertTokenizer
    from transformers import pipeline
    TRANSFORMERS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ transformers库未安装: {e}")
    print("将使用基于规则的后备方案")
    TRANSFORMERS_AVAILABLE = False

logger = logging.getLogger(__name__)

class DynamicChineseBARTSummarizer:
    def __init__(self, model_name="fnlp/bart-base-chinese"):
        """
        初始化中文BART摘要生成器
        
        Args:
            model_name: BART模型名称
        """
        print(f"正在初始化BART摘要生成器: {model_name}")
        self.model_name = model_name
        self.summarizer = None
        
        if TRANSFORMERS_AVAILABLE:
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
            self._initialize_bart_model()
        else:
            self.device = "cpu"
            print("⚠️ 使用基于规则的后备方案")
    
    def _initialize_bart_model(self):
        """初始化BART模型"""
        try:
            print(f"正在加载模型: {self.model_name}")
            print(f"使用设备: {self.device}")
            
            # 使用pipeline方式初始化，这是最稳定的方法
            self.summarizer = pipeline(
                "summarization",
                model=self.model_name,
                tokenizer=self.model_name,
                device=0 if self.device == "cuda" else -1,
                framework="pt"
            )
            
            print("✅ BART模型加载成功")
            
        except Exception as e:
            print(f"⚠️ BART模型加载失败: {e}")
            print("将使用基于规则的后备方案")
            self.summarizer = None
    
    def clean_text(self, text: str) -> str:
        """清理文本内容"""
        if not text:
            return ""
        
        # 去除HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        
        # 去除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 去除特殊字符（保留中文、英文、数字、基本标点）
        text = re.sub(r'[^\u4e00-\u9fff\w\s，。！？；：""''（）【】《》、]', '', text)
        
        return text.strip()
    
    def _fallback_summarize(self, text: str, ratio: float = 0.3) -> str:
        """后备摘要方法：基于规则的文本提取"""
        # 按句子分割
        sentences = re.split(r'[。！？；]', text)
        sentences = [s.strip() for s in sentences if s.strip() and len(s.strip()) > 5]
        
        if not sentences:
            return text[:200] + "..." if len(text) > 200 else text
        
        # 计算需要保留的句子数量
        target_count = max(1, int(len(sentences) * ratio))
        
        # 定义法律文书关键词
        keywords = [
            '认定', '责任', '赔偿', '损失', '事故', '违法', '过错', 
            '判决', '裁定', '支持', '驳回', '承担', '保险', '医疗费',
            '误工费', '护理费', '交通费', '精神损害', '伤残', '死亡'
        ]
        
        # 选择包含关键词的句子
        key_sentences = []
        for sentence in sentences:
            if any(keyword in sentence for keyword in keywords):
                key_sentences.append(sentence)
                if len(key_sentences) >= target_count:
                    break
        
        # 如果关键词句子不够，补充前面的句子
        if len(key_sentences) < target_count:
            for sentence in sentences:
                if sentence not in key_sentences:
                    key_sentences.append(sentence)
                    if len(key_sentences) >= target_count:
                        break
        
        summary = '。'.join(key_sentences[:target_count])
        if summary and not summary.endswith(('。', '！', '？')):
            summary += '。'
        
        return summary
    
    def summarize(self, text: str, custom_ratio: float = 0.3, verbose: bool = True) -> Dict[str, Any]:
        """生成文本摘要"""
        if not text or not text.strip():
            return {
                'summary': '',
                'original_length': 0,
                'summary_length': 0,
                'compression_ratio': 0,
                'method': 'empty'
            }
        
        # 清理文本
        cleaned_text = self.clean_text(text)
        original_length = len(cleaned_text)
        
        # 如果文本太短，直接返回
        if original_length < 50:
            return {
                'summary': cleaned_text,
                'original_length': original_length,
                'summary_length': original_length,
                'compression_ratio': 1.0,
                'method': 'no_compression'
            }
        
        summary = ""
        method = "unknown"
        
        # 尝试使用BART模型
        if self.summarizer:
            try:
                # 计算目标长度 - 修复参数配置
                target_length = max(30, int(original_length * custom_ratio))
                max_length = min(512, max(100, target_length + 20))  # BART模型限制，至少100
                min_length = max(10, min(max_length - 20, target_length - 10))  # 确保min_length < max_length
                
                # 使用BART生成摘要
                result = self.summarizer(
                    cleaned_text,
                    min_length=min_length,
                    max_length=max_length,
                    do_sample=False,
                    truncation=True,
                    clean_up_tokenization_spaces=True
                )
                
                summary = result[0]['summary_text']
                method = "bart_model"
                
            except Exception as e:
                if verbose:
                    print(f"BART模型生成摘要失败: {e}")
                summary = ""
        
        # 如果BART失败，使用后备方法
        if not summary:
            summary = self._fallback_summarize(cleaned_text, custom_ratio)
            method = "fallback_extraction"
        
        # 计算统计信息
        summary_length = len(summary)
        compression_ratio = summary_length / original_length if original_length > 0 else 0
        
        return {
            'summary': summary,
            'original_length': original_length,
            'summary_length': summary_length,
            'compression_ratio': compression_ratio,
            'method': method
        }


# 测试函数
def test_summarizer():
    """测试摘要生成器"""
    print("测试DynamicChineseBARTSummarizer...")
    
    # 创建摘要生成器
    summarizer = DynamicChineseBARTSummarizer()
    
    # 测试文本
    test_text = """
    本案是一起机动车交通事故责任纠纷案件。2024年3月15日上午8时30分许，
    被告张某驾驶小型轿车沿某市某路由东向西行驶至某路口时，与原告李某驾驶的
    电动自行车发生碰撞，造成李某受伤及两车不同程度损坏的交通事故。
    经交警部门认定，被告张某负事故主要责任，原告李某负事故次要责任。
    事故发生后，原告李某被送往医院治疗，产生医疗费用15000元，
    误工费8000元，护理费3000元等各项损失。现原告要求被告承担相应的赔偿责任。
    """
    
    # 生成摘要
    result = summarizer.summarize(test_text, custom_ratio=0.3)
    
    print(f"原文长度: {result['original_length']} 字符")
    print(f"摘要长度: {result['summary_length']} 字符")
    print(f"压缩率: {result['compression_ratio']:.1%}")
    print(f"生成方法: {result['method']}")
    print(f"摘要内容: {result['summary']}")


if __name__ == "__main__":
    test_summarizer()
