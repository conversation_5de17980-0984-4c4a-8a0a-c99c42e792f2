#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交通事故案例搜索系统启动脚本
确保使用正确的环境和参数运行shibing624/text2vec-base-chinese模型
"""

import os
import sys
import logging

# 设置环境变量解决OpenMP冲突
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'
os.environ['OMP_NUM_THREADS'] = '1'
os.environ['MKL_NUM_THREADS'] = '1'
os.environ['NUMEXPR_NUM_THREADS'] = '1'
os.environ['OPENBLAS_NUM_THREADS'] = '1'

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_environment():
    """检查环境是否正确"""
    logger.info("检查Python环境...")
    logger.info(f"Python版本: {sys.version}")
    logger.info(f"Python路径: {sys.executable}")
    
    # 检查必要的包
    required_packages = [
        ('numpy', 'numpy'),
        ('faiss', 'faiss'),
        ('sentence_transformers', 'sentence_transformers'),
        ('beautifulsoup4', 'bs4')
    ]

    missing_packages = []
    for package_name, import_name in required_packages:
        try:
            __import__(import_name)
            logger.info(f"✓ {package_name} 已安装")
        except ImportError:
            missing_packages.append(package_name)
            logger.error(f"✗ {package_name} 未安装")
    
    if missing_packages:
        logger.error(f"缺少必要的包: {missing_packages}")
        return False
    
    return True

def build_index():
    """构建索引"""
    try:
        from traffic_accident_indexer import TrafficAccidentIndexer
        
        logger.info("开始构建索引...")
        
        # 创建索引器
        indexer = TrafficAccidentIndexer()
        
        # 加载数据
        logger.info("正在加载数据...")
        indexer.load_data("traffic_accident_cases.json")
        
        # 构建索引（使用大批处理提高效率）
        logger.info("正在构建向量索引...")
        indexer.build_index(batch_size=256)
        
        # 保存索引
        logger.info("正在保存索引...")
        indexer.save_index()
        
        logger.info("✅ 索引构建完成！")
        return indexer
        
    except Exception as e:
        logger.error(f"❌ 索引构建失败: {e}")
        raise

def test_search(indexer):
    """测试搜索功能"""
    try:
        logger.info("开始测试搜索功能...")
        
        test_queries = [
            "交通事故责任认定",
            "机动车保险赔偿",
            "人身损害赔偿"
        ]
        
        for query in test_queries:
            logger.info(f"测试查询: '{query}'")
            results = indexer.search(query, top_k=3)
            
            logger.info(f"找到 {len(results)} 个结果:")
            for i, (score, case) in enumerate(results, 1):
                case_number = case['original_data'].get('s7', '未知案号')
                logger.info(f"  {i}. 相似度: {score:.4f}, 案号: {case_number}")
        
        logger.info("✅ 搜索测试完成！")
        
    except Exception as e:
        logger.error(f"❌ 搜索测试失败: {e}")
        raise

def interactive_search():
    """交互式搜索"""
    try:
        from traffic_accident_indexer import TrafficAccidentIndexer
        
        # 加载已保存的索引
        indexer = TrafficAccidentIndexer()
        indexer.load_index()
        
        logger.info("索引加载成功，开始交互式搜索...")
        print("\n=== 交通事故案例智能搜索系统 ===")
        print("输入查询内容，按回车搜索，输入 'quit' 退出")
        
        while True:
            try:
                query = input("\n请输入搜索关键词: ").strip()
                
                if query.lower() in ['quit', 'exit', '退出', 'q']:
                    break
                
                if not query:
                    continue
                
                print(f"\n正在搜索: '{query}'...")
                results = indexer.search(query, top_k=5)
                
                if not results:
                    print("未找到相关案例")
                    continue
                
                print(f"\n找到 {len(results)} 个相关案例:")
                print("-" * 80)
                
                for i, (score, case) in enumerate(results, 1):
                    case_number = case['original_data'].get('s7', '未知案号')
                    summary = case['original_data'].get('s25_summary', '无摘要')
                    if len(summary) > 100:
                        summary = summary[:100] + "..."
                    
                    print(f"\n{i}. 案号: {case_number}")
                    print(f"   相似度: {score:.4f}")
                    print(f"   摘要: {summary}")
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"搜索出错: {e}")
        
        print("\n感谢使用！")
        
    except Exception as e:
        logger.error(f"交互式搜索失败: {e}")
        raise

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python start_indexer.py build      # 构建索引")
        print("  python start_indexer.py search     # 交互式搜索")
        print("  python start_indexer.py test       # 构建索引并测试")
        return
    
    command = sys.argv[1].lower()
    
    # 检查环境
    if not check_environment():
        logger.error("环境检查失败，请安装必要的依赖包")
        return
    
    try:
        if command == 'build':
            # 仅构建索引
            indexer = build_index()
            
        elif command == 'search':
            # 交互式搜索
            interactive_search()
            
        elif command == 'test':
            # 构建索引并测试
            indexer = build_index()
            test_search(indexer)
            
        else:
            print(f"未知命令: {command}")
            print("可用命令: build, search, test")
    
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
