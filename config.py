#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交通事故案例搜索系统配置文件

包含系统的各种配置参数和设置
"""

import os

# ===== 模型配置 =====
# 预训练模型名称
MODEL_NAME = "shibing624/text2vec-base-chinese"

# 向量维度 (text2vec-base-chinese的默认维度)
VECTOR_DIMENSION = 768

# 模型缓存目录
MODEL_CACHE_DIR = os.path.expanduser("~/.cache/huggingface/transformers")

# ===== 数据处理配置 =====
# 默认数据文件路径
DEFAULT_DATA_FILE = "traffic_accident_cases.json"

# 文本最大长度限制 (字符数)
MAX_TEXT_LENGTH = 5000

# 批处理大小 (根据内存情况调整)
DEFAULT_BATCH_SIZE = 32

# 小内存环境的批处理大小
SMALL_MEMORY_BATCH_SIZE = 8

# ===== 索引配置 =====
# 默认索引保存目录
DEFAULT_INDEX_DIR = "faiss_index"

# 索引文件名
INDEX_FILE_NAME = "traffic_accident.index"

# 数据文件名
DATA_FILE_NAME = "case_data.pkl"

# 配置文件名
CONFIG_FILE_NAME = "config.json"

# ===== 搜索配置 =====
# 默认搜索结果数量
DEFAULT_TOP_K = 5

# 默认最小相似度阈值
DEFAULT_MIN_SIMILARITY = 0.0

# 最大搜索结果数量
MAX_SEARCH_RESULTS = 100

# ===== 日志配置 =====
# 日志文件名
LOG_FILE_NAME = "traffic_accident_indexer.log"

# 日志级别
LOG_LEVEL = "INFO"

# 日志格式
LOG_FORMAT = '%(asctime)s - %(levelname)s - %(message)s'

# ===== 性能配置 =====
# 是否启用进度条
SHOW_PROGRESS_BAR = True

# 内存使用警告阈值 (GB)
MEMORY_WARNING_THRESHOLD = 8.0

# 搜索超时时间 (秒)
SEARCH_TIMEOUT = 30

# ===== 文本处理配置 =====
# HTML清理配置
HTML_PARSER = 'html.parser'  # 可选: 'lxml', 'html5lib'

# 文本清理正则表达式
TEXT_CLEANUP_PATTERNS = [
    (r'\s+', ' '),  # 多个空白字符替换为单个空格
    (r'^\s+|\s+$', ''),  # 去除首尾空白
]

# 需要提取的字段优先级 (按重要性排序)
TEXT_FIELD_PRIORITY = [
    's25',  # 案例详细描述
    's25_summary',  # 案例摘要
    'qwContent',  # HTML完整内容
    's7',  # 案例编号
]

# ===== 导出配置 =====
# 默认导出文件名
DEFAULT_EXPORT_FILE = "search_results.json"

# 导出文件编码
EXPORT_ENCODING = 'utf-8'

# JSON导出格式化
JSON_INDENT = 2

# ===== 系统配置 =====
# 支持的文件格式
SUPPORTED_DATA_FORMATS = ['.json']

# 临时文件目录
TEMP_DIR = "temp"

# 备份目录
BACKUP_DIR = "backup"

# ===== 错误处理配置 =====
# 最大重试次数
MAX_RETRY_ATTEMPTS = 3

# 重试间隔 (秒)
RETRY_INTERVAL = 1

# 是否在错误时继续处理
CONTINUE_ON_ERROR = True

# ===== 开发和调试配置 =====
# 调试模式
DEBUG_MODE = False

# 详细输出模式
VERBOSE_MODE = False

# 性能分析模式
PROFILING_MODE = False

# 测试模式配置
TEST_MODE_CONFIG = {
    'batch_size': 4,
    'max_cases': 100,
    'index_dir': 'test_index',
    'log_file': 'test.log'
}

# ===== 环境检测和自适应配置 =====
def get_optimal_batch_size():
    """根据系统内存自动确定最优批处理大小"""
    try:
        import psutil
        memory_gb = psutil.virtual_memory().total / (1024**3)
        
        if memory_gb >= 16:
            return 64
        elif memory_gb >= 8:
            return 32
        elif memory_gb >= 4:
            return 16
        else:
            return 8
    except ImportError:
        # 如果没有psutil，使用默认值
        return DEFAULT_BATCH_SIZE

def get_device_config():
    """检测可用的计算设备"""
    config = {
        'device': 'cpu',
        'use_gpu': False,
        'gpu_memory': 0
    }
    
    try:
        import torch
        if torch.cuda.is_available():
            config['device'] = 'cuda'
            config['use_gpu'] = True
            config['gpu_memory'] = torch.cuda.get_device_properties(0).total_memory / (1024**3)
    except ImportError:
        pass
    
    return config

# ===== 运行时配置 =====
# 自动检测最优配置
OPTIMAL_BATCH_SIZE = get_optimal_batch_size()
DEVICE_CONFIG = get_device_config()

# 根据环境调整配置
if DEVICE_CONFIG['use_gpu']:
    # GPU环境可以使用更大的批处理
    DEFAULT_BATCH_SIZE = min(OPTIMAL_BATCH_SIZE * 2, 128)
else:
    DEFAULT_BATCH_SIZE = OPTIMAL_BATCH_SIZE

# ===== 配置验证函数 =====
def validate_config():
    """验证配置的有效性"""
    errors = []
    
    # 检查必要的目录
    required_dirs = [DEFAULT_INDEX_DIR, TEMP_DIR]
    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            try:
                os.makedirs(dir_path, exist_ok=True)
            except Exception as e:
                errors.append(f"无法创建目录 {dir_path}: {e}")
    
    # 检查批处理大小
    if DEFAULT_BATCH_SIZE <= 0:
        errors.append("批处理大小必须大于0")
    
    # 检查向量维度
    if VECTOR_DIMENSION <= 0:
        errors.append("向量维度必须大于0")
    
    return errors

# ===== 配置信息打印函数 =====
def print_config_info():
    """打印当前配置信息"""
    print("=== 系统配置信息 ===")
    print(f"模型名称: {MODEL_NAME}")
    print(f"向量维度: {VECTOR_DIMENSION}")
    print(f"批处理大小: {DEFAULT_BATCH_SIZE}")
    print(f"计算设备: {DEVICE_CONFIG['device']}")
    print(f"GPU可用: {'是' if DEVICE_CONFIG['use_gpu'] else '否'}")
    print(f"索引目录: {DEFAULT_INDEX_DIR}")
    print(f"日志文件: {LOG_FILE_NAME}")
    print("=" * 30)

if __name__ == "__main__":
    # 验证配置
    errors = validate_config()
    if errors:
        print("配置错误:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("配置验证通过")
    
    # 打印配置信息
    print_config_info()
