#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交通事故案例向量化搜索系统
基于text2vec-base-chinese模型和Faiss库实现高效的相似性搜索

主要功能：
1. 加载并解析traffic_accident_cases.json文件
2. 使用text2vec-base-chinese模型对文本进行向量化编码
3. 创建Faiss索引并存储向量数据
4. 提供保存和加载索引的功能
5. 实现基于向量相似度的搜索接口

作者: AI Assistant
创建时间: 2025-09-11
"""

import json
import logging
import os
import pickle
import re
import gc
from typing import List, Dict, Tuple, Optional, Any
import numpy as np
import faiss
from sentence_transformers import SentenceTransformer
from bs4 import BeautifulSoup
import warnings

# 设置环境变量解决OpenMP冲突
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'
os.environ['OMP_NUM_THREADS'] = '1'
os.environ['MKL_NUM_THREADS'] = '1'
os.environ['NUMEXPR_NUM_THREADS'] = '1'

# 忽略一些不重要的警告
warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings("ignore", category=UserWarning)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('traffic_accident_indexer.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class TrafficAccidentIndexer:
    """交通事故案例向量化索引器"""
    
    def __init__(self, model_name: str = "shibing624/text2vec-base-chinese"):
        """
        初始化索引器
        
        Args:
            model_name: 预训练模型名称，默认使用text2vec-base-chinese
        """
        self.model_name = model_name
        self.model = None
        self.index = None
        self.case_data = []
        self.dimension = 768  # text2vec-base-chinese的向量维度
        
        logger.info(f"初始化TrafficAccidentIndexer，使用模型: {model_name}")
        
    def _load_model(self):
        """加载预训练模型"""
        try:
            logger.info("正在加载预训练模型...")
            self.model = SentenceTransformer(self.model_name)
            logger.info("模型加载成功")
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise
    
    def _clean_html_text(self, html_content: str) -> str:
        """
        清理HTML内容，提取纯文本
        
        Args:
            html_content: HTML格式的文本内容
            
        Returns:
            清理后的纯文本
        """
        try:
            # 使用BeautifulSoup解析HTML
            soup = BeautifulSoup(html_content, 'html.parser')
            text = soup.get_text()
            
            # 清理多余的空白字符
            text = re.sub(r'\s+', ' ', text)
            text = text.strip()
            
            return text
        except Exception as e:
            logger.warning(f"HTML清理失败: {e}")
            return html_content
    
    def _extract_text_fields(self, case: Dict[str, Any]) -> str:
        """
        从案例数据中提取关键文本字段

        Args:
            case: 单个案例数据

        Returns:
            合并后的文本内容
        """
        text_parts = []

        # 只使用s25字段内容
        if 's25' in case and case['s25']:
            text_parts.append(case['s25'])

        # 合并所有文本，用空格分隔
        combined_text = ' '.join(text_parts)

        # 限制文本长度，避免过长的文本影响向量化效果
        # if len(combined_text) > 5000:
        #     combined_text = combined_text[:5000]

        return combined_text
    
    def load_data(self, json_file_path: str) -> None:
        """
        加载JSON数据文件
        
        Args:
            json_file_path: JSON文件路径
        """
        try:
            logger.info(f"正在加载数据文件: {json_file_path}")
            
            with open(json_file_path, 'r', encoding='utf-8') as f:
                raw_data = json.load(f)
            
            # 处理数据，提取文本内容
            self.case_data = []
            for i, case in enumerate(raw_data):
                try:
                    text_content = self._extract_text_fields(case)
                    if text_content.strip():  # 确保有有效内容
                        processed_case = {
                            'id': i,
                            'text': text_content,
                            'original_data': case
                        }
                        self.case_data.append(processed_case)
                except Exception as e:
                    logger.warning(f"处理第{i}个案例时出错: {e}")
                    continue
            
            logger.info(f"成功加载 {len(self.case_data)} 个有效案例")
            
        except Exception as e:
            logger.error(f"数据加载失败: {e}")
            raise
    
    def build_index(self, batch_size: int = 256) -> None:
        """
        构建Faiss向量索引 - 使用单个文本处理避免段错误

        Args:
            batch_size: 批处理大小，默认为1以确保稳定性
        """
        if not self.case_data:
            raise ValueError("请先加载数据")

        if self.model is None:
            self._load_model()

        try:
            logger.info("开始构建向量索引...")

            # 创建Faiss索引 (使用L2距离的平面索引)
            self.index = faiss.IndexFlatL2(self.dimension)

            # 分批处理文本向量化
            all_vectors = []
            total_cases = len(self.case_data)
            total_batches = (total_cases + batch_size - 1) // batch_size

            for batch_idx in range(total_batches):
                start_idx = batch_idx * batch_size
                end_idx = min(start_idx + batch_size, total_cases)

                batch_texts = [case['text'] for case in self.case_data[start_idx:end_idx]]

                logger.info(f"正在处理批次 {batch_idx + 1}/{total_batches} "
                           f"(案例 {start_idx + 1}-{end_idx})")

                try:
                    # 批量向量化文本
                    batch_vectors = self.model.encode(
                        batch_texts,
                        batch_size=batch_size,
                        show_progress_bar=False,
                        convert_to_numpy=True,
                        normalize_embeddings=False,
                        device='cpu'
                    )

                    all_vectors.append(batch_vectors)

                    # 定期清理内存
                    if batch_idx % 10 == 0:
                        gc.collect()

                except Exception as e:
                    logger.warning(f"批次 {batch_idx + 1} 向量化失败: {e}")
                    # 尝试逐个处理这个批次
                    batch_vectors_list = []
                    for i, text in enumerate(batch_texts):
                        try:
                            single_vector = self.model.encode(
                                [text],
                                batch_size=1,
                                show_progress_bar=False,
                                convert_to_numpy=True,
                                normalize_embeddings=False,
                                device='cpu'
                            )
                            batch_vectors_list.append(single_vector)
                        except Exception as single_e:
                            logger.warning(f"单个文本向量化失败: {single_e}")
                            # 使用零向量作为占位符
                            zero_vector = np.zeros((1, self.dimension), dtype='float32')
                            batch_vectors_list.append(zero_vector)

                    # 合并这个批次的向量
                    if batch_vectors_list:
                        batch_vectors = np.vstack(batch_vectors_list)
                        all_vectors.append(batch_vectors)

            # 合并所有向量
            logger.info("正在合并向量...")
            vectors = np.vstack(all_vectors).astype('float32')

            # 添加向量到索引
            logger.info("正在添加向量到索引...")
            self.index.add(vectors)

            logger.info(f"索引构建完成，共包含 {self.index.ntotal} 个向量")

        except Exception as e:
            logger.error(f"索引构建失败: {e}")
            raise
    
    def save_index(self, index_dir: str = "faiss_index") -> None:
        """
        保存索引和相关数据
        
        Args:
            index_dir: 保存索引的目录
        """
        try:
            os.makedirs(index_dir, exist_ok=True)
            
            # 保存Faiss索引
            index_path = os.path.join(index_dir, "traffic_accident.index")
            faiss.write_index(self.index, index_path)
            
            # 保存案例数据
            data_path = os.path.join(index_dir, "case_data.pkl")
            with open(data_path, 'wb') as f:
                pickle.dump(self.case_data, f)
            
            # 保存配置信息
            config_path = os.path.join(index_dir, "config.json")
            config = {
                'model_name': self.model_name,
                'dimension': self.dimension,
                'total_cases': len(self.case_data)
            }
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            logger.info(f"索引已保存到: {index_dir}")
            
        except Exception as e:
            logger.error(f"索引保存失败: {e}")
            raise
    
    def load_index(self, index_dir: str = "faiss_index") -> None:
        """
        加载已保存的索引
        
        Args:
            index_dir: 索引目录
        """
        try:
            # 加载配置
            config_path = os.path.join(index_dir, "config.json")
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            self.model_name = config['model_name']
            self.dimension = config['dimension']
            
            # 加载模型
            if self.model is None:
                self._load_model()
            
            # 加载Faiss索引
            index_path = os.path.join(index_dir, "traffic_accident.index")
            self.index = faiss.read_index(index_path)
            
            # 加载案例数据
            data_path = os.path.join(index_dir, "case_data.pkl")
            with open(data_path, 'rb') as f:
                self.case_data = pickle.load(f)
            
            logger.info(f"索引加载成功，包含 {len(self.case_data)} 个案例")
            
        except Exception as e:
            logger.error(f"索引加载失败: {e}")
            raise
    
    def search(self, query: str, top_k: int = 5) -> List[Tuple[float, Dict[str, Any]]]:
        """
        基于向量相似度搜索相关案例

        Args:
            query: 查询文本
            top_k: 返回最相似的前k个结果

        Returns:
            包含相似度分数和案例数据的元组列表
        """
        if self.index is None:
            raise ValueError("请先构建或加载索引")

        if self.model is None:
            self._load_model()

        try:
            # 将查询文本向量化，使用安全参数
            query_vector = self.model.encode(
                [query],
                batch_size=1,
                show_progress_bar=False,
                convert_to_numpy=True,
                normalize_embeddings=False,
                device='cpu'
            ).astype('float32')

            # 在索引中搜索
            distances, indices = self.index.search(query_vector, top_k)

            # 整理搜索结果
            results = []
            for distance, idx in zip(distances[0], indices[0]):
                if idx < len(self.case_data):
                    similarity_score = 1.0 / (1.0 + distance)  # 转换为相似度分数
                    case_info = self.case_data[idx]
                    results.append((similarity_score, case_info))

            logger.info(f"搜索完成，返回 {len(results)} 个结果")
            return results

        except Exception as e:
            logger.error(f"搜索失败: {e}")
            raise


def main():
    """主函数 - 使用示例"""
    try:
        # 创建索引器实例
        indexer = TrafficAccidentIndexer()
        
        # 加载数据
        indexer.load_data("traffic_accident_cases.json")
        
        # 构建索引
        indexer.build_index(batch_size=16)  # 可根据内存情况调整批次大小
        
        # 保存索引
        indexer.save_index()
        
        # 测试搜索功能
        test_queries = [
            "交通事故责任认定",
            "机动车保险赔偿",
            "人身损害赔偿",
            "误工费计算标准"
        ]
        
        print("\n=== 搜索测试 ===")
        for query in test_queries:
            print(f"\n查询: {query}")
            results = indexer.search(query, top_k=3)
            
            for i, (score, case) in enumerate(results, 1):
                case_number = case['original_data'].get('s7', '未知案号')
                summary = case['original_data'].get('s25_summary', '无摘要')[:100]
                print(f"  {i}. 相似度: {score:.4f}")
                print(f"     案号: {case_number}")
                print(f"     摘要: {summary}...")
                print()
        
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        raise

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取索引统计信息

        Returns:
            包含统计信息的字典
        """
        stats = {
            'total_cases': len(self.case_data),
            'model_name': self.model_name,
            'vector_dimension': self.dimension,
            'index_size': self.index.ntotal if self.index else 0,
            'average_text_length': 0
        }

        if self.case_data:
            total_length = sum(len(case['text']) for case in self.case_data)
            stats['average_text_length'] = total_length / len(self.case_data)

        return stats

    def search_with_filters(self, query: str, top_k: int = 5,
                           case_number_filter: Optional[str] = None,
                           min_similarity: float = 0.0) -> List[Tuple[float, Dict[str, Any]]]:
        """
        带过滤条件的搜索

        Args:
            query: 查询文本
            top_k: 返回最相似的前k个结果
            case_number_filter: 案号过滤条件（部分匹配）
            min_similarity: 最小相似度阈值

        Returns:
            过滤后的搜索结果
        """
        # 先进行基本搜索
        results = self.search(query, top_k * 2)  # 获取更多结果用于过滤

        filtered_results = []
        for score, case in results:
            # 相似度过滤
            if score < min_similarity:
                continue

            # 案号过滤
            if case_number_filter:
                case_number = case['original_data'].get('s7', '')
                if case_number_filter.lower() not in case_number.lower():
                    continue

            filtered_results.append((score, case))

            # 达到所需数量就停止
            if len(filtered_results) >= top_k:
                break

        return filtered_results

    def export_search_results(self, results: List[Tuple[float, Dict[str, Any]]],
                             output_file: str = "search_results.json") -> None:
        """
        导出搜索结果到JSON文件

        Args:
            results: 搜索结果
            output_file: 输出文件路径
        """
        try:
            export_data = []
            for score, case in results:
                export_item = {
                    'similarity_score': float(score),
                    'case_id': case['id'],
                    'case_number': case['original_data'].get('s7', ''),
                    'summary': case['original_data'].get('s25_summary', ''),
                    'full_content': case['original_data'].get('s25', ''),
                    'line_number': case['original_data'].get('line_number', 0)
                }
                export_data.append(export_item)

            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)

            logger.info(f"搜索结果已导出到: {output_file}")

        except Exception as e:
            logger.error(f"导出搜索结果失败: {e}")
            raise


class TrafficAccidentSearchEngine:
    """交通事故案例搜索引擎 - 高级封装类"""

    def __init__(self, index_dir: str = "faiss_index"):
        """
        初始化搜索引擎

        Args:
            index_dir: 索引目录路径
        """
        self.indexer = TrafficAccidentIndexer()
        self.index_dir = index_dir
        self.is_loaded = False

    def initialize(self, json_file_path: Optional[str] = None,
                  force_rebuild: bool = False) -> None:
        """
        初始化搜索引擎

        Args:
            json_file_path: JSON数据文件路径（如果需要重建索引）
            force_rebuild: 是否强制重建索引
        """
        try:
            # 检查是否存在已保存的索引
            if not force_rebuild and os.path.exists(self.index_dir):
                logger.info("发现已存在的索引，正在加载...")
                self.indexer.load_index(self.index_dir)
                self.is_loaded = True
            else:
                if not json_file_path:
                    raise ValueError("首次构建索引需要提供JSON数据文件路径")

                logger.info("构建新的索引...")
                self.indexer.load_data(json_file_path)
                self.indexer.build_index()
                self.indexer.save_index(self.index_dir)
                self.is_loaded = True

            logger.info("搜索引擎初始化完成")

        except Exception as e:
            logger.error(f"搜索引擎初始化失败: {e}")
            raise

    def search(self, query: str, **kwargs) -> List[Dict[str, Any]]:
        """
        搜索接口

        Args:
            query: 查询文本
            **kwargs: 其他搜索参数

        Returns:
            格式化的搜索结果
        """
        if not self.is_loaded:
            raise ValueError("搜索引擎未初始化，请先调用initialize()方法")

        # 执行搜索
        raw_results = self.indexer.search_with_filters(query, **kwargs)

        # 格式化结果
        formatted_results = []
        for score, case in raw_results:
            result = {
                'similarity_score': round(score, 4),
                'case_number': case['original_data'].get('s7', '未知案号'),
                'summary': case['original_data'].get('s25_summary', '无摘要'),
                'content_preview': case['text'][:200] + "..." if len(case['text']) > 200 else case['text'],
                'line_number': case['original_data'].get('line_number', 0),
                'full_data': case['original_data']
            }
            formatted_results.append(result)

        return formatted_results

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        if not self.is_loaded:
            return {"error": "搜索引擎未初始化"}

        return self.indexer.get_statistics()


def interactive_search_demo():
    """交互式搜索演示"""
    print("=== 交通事故案例智能搜索系统 ===")
    print("正在初始化搜索引擎...")

    try:
        # 初始化搜索引擎
        engine = TrafficAccidentSearchEngine()
        engine.initialize("traffic_accident_cases.json")

        # 显示统计信息
        stats = engine.get_stats()
        print(f"\n系统信息:")
        print(f"- 案例总数: {stats['total_cases']}")
        print(f"- 向量维度: {stats['vector_dimension']}")
        print(f"- 平均文本长度: {stats['average_text_length']:.0f} 字符")

        print("\n开始交互式搜索 (输入 'quit' 退出):")

        while True:
            query = input("\n请输入搜索关键词: ").strip()

            if query.lower() in ['quit', 'exit', '退出']:
                break

            if not query:
                continue

            try:
                # 执行搜索
                results = engine.search(query, top_k=3, min_similarity=0.1)

                if not results:
                    print("未找到相关案例")
                    continue

                print(f"\n找到 {len(results)} 个相关案例:")
                print("-" * 80)

                for i, result in enumerate(results, 1):
                    print(f"\n{i}. 案号: {result['case_number']}")
                    print(f"   相似度: {result['similarity_score']}")
                    print(f"   摘要: {result['summary'][:150]}...")
                    print(f"   预览: {result['content_preview']}")

            except Exception as e:
                print(f"搜索出错: {e}")

    except Exception as e:
        print(f"系统初始化失败: {e}")


def batch_search_demo():
    """批量搜索演示"""
    print("=== 批量搜索演示 ===")

    # 预定义的测试查询
    test_queries = [
        "交通事故责任认定主要责任",
        "机动车保险公司赔偿标准",
        "人身损害赔偿误工费计算",
        "道路交通事故伤残鉴定",
        "保险理赔拒赔案例",
        "交通肇事逃逸处理",
        "车辆挂靠经营责任承担",
        "交强险赔偿限额范围"
    ]

    try:
        # 初始化搜索引擎
        engine = TrafficAccidentSearchEngine()
        engine.initialize("traffic_accident_cases.json")

        print(f"开始批量搜索，共 {len(test_queries)} 个查询...")

        all_results = {}
        for i, query in enumerate(test_queries, 1):
            print(f"\n[{i}/{len(test_queries)}] 搜索: {query}")

            results = engine.search(query, top_k=2, min_similarity=0.2)
            all_results[query] = results

            if results:
                print(f"  找到 {len(results)} 个相关案例")
                for j, result in enumerate(results, 1):
                    print(f"    {j}. {result['case_number']} (相似度: {result['similarity_score']})")
            else:
                print("  未找到相关案例")

        # 导出结果
        print("\n正在导出搜索结果...")
        export_data = []
        for query, results in all_results.items():
            for result in results:
                export_item = {
                    'query': query,
                    'similarity_score': result['similarity_score'],
                    'case_number': result['case_number'],
                    'summary': result['summary']
                }
                export_data.append(export_item)

        with open('batch_search_results.json', 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)

        print("批量搜索完成，结果已保存到 batch_search_results.json")

    except Exception as e:
        print(f"批量搜索失败: {e}")


def main():
    """主函数 - 提供多种使用模式"""
    import sys

    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()

        if mode == 'interactive':
            interactive_search_demo()
        elif mode == 'batch':
            batch_search_demo()
        elif mode == 'build':
            # 仅构建索引
            print("构建索引...")
            indexer = TrafficAccidentIndexer()
            indexer.load_data("traffic_accident_cases.json")
            indexer.build_index(batch_size=4)  # 使用更小的批处理大小
            indexer.save_index()
            print("索引构建完成")
        else:
            print("使用方法:")
            print("  python traffic_accident_indexer.py interactive  # 交互式搜索")
            print("  python traffic_accident_indexer.py batch       # 批量搜索演示")
            print("  python traffic_accident_indexer.py build       # 仅构建索引")
    else:
        # 默认运行原始示例
        try:
            # 创建索引器实例
            indexer = TrafficAccidentIndexer()

            # 加载数据
            indexer.load_data("traffic_accident_cases.json")

            # 构建索引
            indexer.build_index(batch_size=4)  # 可根据内存情况调整批次大小

            # 保存索引
            indexer.save_index()

            # 测试搜索功能
            test_queries = [
                "交通事故责任认定",
                "机动车保险赔偿",
                "人身损害赔偿",
                "误工费计算标准"
            ]

            print("\n=== 搜索测试 ===")
            for query in test_queries:
                print(f"\n查询: {query}")
                results = indexer.search(query, top_k=3)

                for i, (score, case) in enumerate(results, 1):
                    case_number = case['original_data'].get('s7', '未知案号')
                    summary = case['original_data'].get('s25_summary', '无摘要')[:100]
                    print(f"  {i}. 相似度: {score:.4f}")
                    print(f"     案号: {case_number}")
                    print(f"     摘要: {summary}...")
                    print()

        except Exception as e:
            logger.error(f"程序执行失败: {e}")
            raise


if __name__ == "__main__":
    main()
