#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交通事故案例搜索系统测试脚本

用于验证系统的基本功能和性能
"""

import time
import json
import os
from traffic_accident_indexer import TrafficAccidentIndexer, TrafficAccidentSearchEngine


def test_basic_functionality():
    """测试基本功能"""
    print("=== 基本功能测试 ===")
    
    try:
        # 测试数据加载
        print("1. 测试数据加载...")
        indexer = TrafficAccidentIndexer()
        indexer.load_data("traffic_accident_cases.json")
        print(f"   ✓ 成功加载 {len(indexer.case_data)} 个案例")
        
        # 测试索引构建
        print("2. 测试索引构建...")
        start_time = time.time()
        indexer.build_index(batch_size=8)  # 使用较小的批次进行测试
        build_time = time.time() - start_time
        print(f"   ✓ 索引构建完成，耗时 {build_time:.2f} 秒")
        
        # 测试搜索功能
        print("3. 测试搜索功能...")
        test_query = "交通事故责任认定"
        start_time = time.time()
        results = indexer.search(test_query, top_k=3)
        search_time = time.time() - start_time
        print(f"   ✓ 搜索完成，耗时 {search_time:.4f} 秒，找到 {len(results)} 个结果")
        
        # 显示搜索结果
        for i, (score, case) in enumerate(results, 1):
            case_number = case['original_data'].get('s7', '未知案号')
            print(f"     {i}. 案号: {case_number}, 相似度: {score:.4f}")
        
        # 测试索引保存
        print("4. 测试索引保存...")
        indexer.save_index("test_index")
        print("   ✓ 索引保存成功")
        
        # 测试索引加载
        print("5. 测试索引加载...")
        new_indexer = TrafficAccidentIndexer()
        new_indexer.load_index("test_index")
        print("   ✓ 索引加载成功")
        
        # 验证加载后的搜索功能
        print("6. 验证加载后的搜索功能...")
        results2 = new_indexer.search(test_query, top_k=3)
        print(f"   ✓ 搜索验证成功，找到 {len(results2)} 个结果")
        
        print("\n✅ 基本功能测试全部通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 基本功能测试失败: {e}")
        return False


def test_advanced_features():
    """测试高级功能"""
    print("\n=== 高级功能测试 ===")
    
    try:
        # 测试搜索引擎
        print("1. 测试搜索引擎初始化...")
        engine = TrafficAccidentSearchEngine("test_index")
        engine.initialize()
        print("   ✓ 搜索引擎初始化成功")
        
        # 测试统计信息
        print("2. 测试统计信息...")
        stats = engine.get_stats()
        print(f"   ✓ 案例总数: {stats['total_cases']}")
        print(f"   ✓ 向量维度: {stats['vector_dimension']}")
        print(f"   ✓ 平均文本长度: {stats['average_text_length']:.0f} 字符")
        
        # 测试带过滤的搜索
        print("3. 测试过滤搜索...")
        results = engine.search("保险赔偿", top_k=5, min_similarity=0.1)
        print(f"   ✓ 过滤搜索完成，找到 {len(results)} 个结果")
        
        # 测试结果导出
        print("4. 测试结果导出...")
        if results:
            # 转换为原始格式用于导出
            raw_results = [(r['similarity_score'], {'original_data': r['full_data'], 'id': 0}) 
                          for r in results]
            engine.indexer.export_search_results(raw_results, "test_results.json")
            print("   ✓ 结果导出成功")
        
        print("\n✅ 高级功能测试全部通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 高级功能测试失败: {e}")
        return False


def test_performance():
    """性能测试"""
    print("\n=== 性能测试 ===")
    
    try:
        engine = TrafficAccidentSearchEngine("test_index")
        engine.initialize()
        
        # 测试查询
        test_queries = [
            "交通事故责任认定",
            "机动车保险赔偿",
            "人身损害赔偿",
            "误工费计算",
            "伤残鉴定标准"
        ]
        
        print(f"执行 {len(test_queries)} 个查询的性能测试...")
        
        total_time = 0
        for i, query in enumerate(test_queries, 1):
            start_time = time.time()
            results = engine.search(query, top_k=5)
            query_time = time.time() - start_time
            total_time += query_time
            
            print(f"  查询 {i}: '{query}' - {query_time:.4f}秒, {len(results)}个结果")
        
        avg_time = total_time / len(test_queries)
        print(f"\n性能统计:")
        print(f"  总耗时: {total_time:.4f} 秒")
        print(f"  平均查询时间: {avg_time:.4f} 秒")
        print(f"  查询吞吐量: {1/avg_time:.2f} 查询/秒")
        
        if avg_time < 1.0:
            print("✅ 性能测试通过 (平均查询时间 < 1秒)")
            return True
        else:
            print("⚠️ 性能警告 (平均查询时间 >= 1秒)")
            return True
            
    except Exception as e:
        print(f"\n❌ 性能测试失败: {e}")
        return False


def cleanup_test_files():
    """清理测试文件"""
    print("\n=== 清理测试文件 ===")
    
    import shutil
    
    files_to_remove = [
        "test_results.json",
        "traffic_accident_indexer.log"
    ]
    
    dirs_to_remove = [
        "test_index"
    ]
    
    for file_path in files_to_remove:
        if os.path.exists(file_path):
            os.remove(file_path)
            print(f"  ✓ 删除文件: {file_path}")
    
    for dir_path in dirs_to_remove:
        if os.path.exists(dir_path):
            shutil.rmtree(dir_path)
            print(f"  ✓ 删除目录: {dir_path}")
    
    print("清理完成")


def main():
    """主测试函数"""
    print("交通事故案例搜索系统 - 功能测试")
    print("=" * 50)
    
    # 检查数据文件是否存在
    if not os.path.exists("traffic_accident_cases.json"):
        print("❌ 错误: 找不到数据文件 'traffic_accident_cases.json'")
        print("请确保数据文件在当前目录中")
        return
    
    test_results = []
    
    # 执行测试
    test_results.append(("基本功能", test_basic_functionality()))
    test_results.append(("高级功能", test_advanced_features()))
    test_results.append(("性能测试", test_performance()))
    
    # 汇总测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统运行正常。")
    else:
        print("⚠️ 部分测试失败，请检查错误信息。")
    
    # 询问是否清理测试文件
    try:
        response = input("\n是否清理测试文件? (y/n): ").lower().strip()
        if response in ['y', 'yes', '是']:
            cleanup_test_files()
    except KeyboardInterrupt:
        print("\n测试结束")


if __name__ == "__main__":
    main()
