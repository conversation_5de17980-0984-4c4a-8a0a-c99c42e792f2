#!/bin/bash

# 设置环境变量解决兼容性问题
export KMP_DUPLICATE_LIB_OK=TRUE
export OMP_NUM_THREADS=1
export MKL_NUM_THREADS=1
export NUMEXPR_NUM_THREADS=1
export OPENBLAS_NUM_THREADS=1

# 使用正确的Python环境
PYTHON_PATH="/opt/anaconda3/envs/search311/bin/python"

echo "设置环境变量完成"
echo "Python路径: $PYTHON_PATH"

# 运行程序
if [ "$1" = "test" ]; then
    echo "运行测试..."
    $PYTHON_PATH simple_test.py
elif [ "$1" = "build" ]; then
    echo "构建索引..."
    $PYTHON_PATH traffic_accident_indexer.py build
elif [ "$1" = "safe" ]; then
    echo "运行安全版本..."
    $PYTHON_PATH safe_indexer.py
else
    echo "使用方法:"
    echo "  ./run_with_env.sh test   # 运行测试"
    echo "  ./run_with_env.sh build  # 构建索引"
    echo "  ./run_with_env.sh safe   # 运行安全版本"
fi
