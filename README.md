# 交通事故案例向量化搜索系统

基于text2vec-base-chinese模型和Faiss库实现的高效交通事故案例相似性搜索系统。

## 功能特性

- 🔍 **智能搜索**: 基于语义相似度的案例搜索，而非简单的关键词匹配
- 🚀 **高性能**: 使用Faiss库实现毫秒级向量搜索
- 🧠 **中文优化**: 专门针对中文法律文本优化的text2vec-base-chinese模型
- 📊 **多种模式**: 支持交互式搜索、批量搜索和API调用
- 💾 **持久化**: 支持索引的保存和加载，避免重复构建
- 🔧 **可扩展**: 模块化设计，易于扩展和定制

## 安装依赖

```bash
pip install -r requirements.txt
```

## 快速开始

### 1. 基本使用

```python
from traffic_accident_indexer import TrafficAccidentIndexer

# 创建索引器
indexer = TrafficAccidentIndexer()

# 加载数据并构建索引
indexer.load_data("traffic_accident_cases.json")
indexer.build_index()

# 保存索引
indexer.save_index()

# 搜索相关案例
results = indexer.search("交通事故责任认定", top_k=5)
for score, case in results:
    print(f"相似度: {score:.4f}")
    print(f"案号: {case['original_data']['s7']}")
    print(f"摘要: {case['original_data']['s25_summary'][:100]}...")
    print("-" * 50)
```

### 2. 使用高级搜索引擎

```python
from traffic_accident_indexer import TrafficAccidentSearchEngine

# 初始化搜索引擎
engine = TrafficAccidentSearchEngine()
engine.initialize("traffic_accident_cases.json")

# 执行搜索
results = engine.search("机动车保险赔偿", top_k=3, min_similarity=0.2)
for result in results:
    print(f"案号: {result['case_number']}")
    print(f"相似度: {result['similarity_score']}")
    print(f"摘要: {result['summary']}")
    print()
```

### 3. 命令行使用

```bash
# 交互式搜索
python traffic_accident_indexer.py interactive

# 批量搜索演示
python traffic_accident_indexer.py batch

# 仅构建索引
python traffic_accident_indexer.py build
```

## 数据格式

系统支持的JSON数据格式：

```json
[
  {
    "line_number": 1,
    "qwContent": "HTML格式的完整案例内容",
    "s25": "案例详细描述",
    "s7": "案例编号",
    "s25_summary": "案例摘要"
  }
]
```

## API 参考

### TrafficAccidentIndexer 类

#### 主要方法

- `load_data(json_file_path)`: 加载JSON数据文件
- `build_index(batch_size=32)`: 构建Faiss向量索引
- `save_index(index_dir="faiss_index")`: 保存索引到磁盘
- `load_index(index_dir="faiss_index")`: 从磁盘加载索引
- `search(query, top_k=5)`: 基础搜索功能
- `search_with_filters(query, top_k=5, case_number_filter=None, min_similarity=0.0)`: 带过滤条件的搜索
- `get_statistics()`: 获取索引统计信息
- `export_search_results(results, output_file)`: 导出搜索结果

### TrafficAccidentSearchEngine 类

#### 主要方法

- `initialize(json_file_path=None, force_rebuild=False)`: 初始化搜索引擎
- `search(query, **kwargs)`: 执行搜索并返回格式化结果
- `get_stats()`: 获取系统统计信息

## 性能优化建议

1. **内存管理**: 根据可用内存调整`batch_size`参数
2. **索引持久化**: 首次构建后保存索引，后续直接加载
3. **文本预处理**: 对于特别长的文本，系统会自动截断到5000字符
4. **GPU加速**: 如有GPU，可安装`faiss-gpu`替代`faiss-cpu`

## 系统要求

- Python 3.7+
- 内存: 建议8GB以上（处理大型数据集时）
- 存储: 索引文件大小约为原始数据的1-2倍

## 故障排除

### 常见问题

1. **内存不足**: 减小`batch_size`参数
2. **模型下载失败**: 检查网络连接，或手动下载模型文件
3. **索引加载失败**: 确保索引文件完整，必要时重新构建

### 日志文件

系统会自动生成`traffic_accident_indexer.log`日志文件，包含详细的运行信息和错误记录。

## 扩展功能

系统支持以下扩展：

- 自定义文本预处理流程
- 不同的向量化模型
- 其他类型的Faiss索引（如IVF、HNSW等）
- 多语言支持

## 许可证

本项目采用MIT许可证。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。
