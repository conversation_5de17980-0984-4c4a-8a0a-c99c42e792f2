2025-09-11 14:59:46,705 - INFO - 初始化TrafficAccidentIndexer，使用模型: shibing624/text2vec-base-chinese
2025-09-11 14:59:46,705 - INFO - 正在加载数据文件: traffic_accident_cases.json
2025-09-11 14:59:51,474 - INFO - 成功加载 8758 个有效案例
2025-09-11 14:59:51,475 - INFO - 正在加载预训练模型...
2025-09-11 14:59:51,475 - INFO - Use pytorch device_name: cpu
2025-09-11 14:59:51,476 - INFO - Load pretrained SentenceTransformer: shibing624/text2vec-base-chinese
2025-09-11 14:59:55,694 - INFO - 模型加载成功
2025-09-11 14:59:55,695 - INFO - 开始构建向量索引...
2025-09-11 14:59:55,695 - INFO - 正在处理批次 1/274 (案例 1-32)
2025-09-11 15:00:51,956 - INFO - 初始化TrafficAccidentIndexer，使用模型: shibing624/text2vec-base-chinese
2025-09-11 15:00:51,956 - INFO - 正在加载数据文件: traffic_accident_cases.json
2025-09-11 15:00:56,656 - INFO - 成功加载 8758 个有效案例
2025-09-11 15:00:56,656 - INFO - 正在加载预训练模型...
2025-09-11 15:00:56,657 - INFO - Use pytorch device_name: cpu
2025-09-11 15:00:56,657 - INFO - Load pretrained SentenceTransformer: shibing624/text2vec-base-chinese
2025-09-11 15:01:01,119 - INFO - 模型加载成功
2025-09-11 15:01:01,119 - INFO - 开始构建向量索引...
2025-09-11 15:01:01,119 - INFO - 正在处理批次 1/2190 (案例 1-4)
2025-09-11 15:02:06,174 - INFO - 初始化TrafficAccidentIndexer，使用模型: shibing624/text2vec-base-chinese
2025-09-11 15:02:06,174 - INFO - 正在加载数据文件: traffic_accident_cases.json
2025-09-11 15:02:10,838 - INFO - 成功加载 8758 个有效案例
2025-09-11 15:02:10,838 - INFO - 正在加载预训练模型...
2025-09-11 15:02:10,839 - INFO - Use pytorch device_name: cpu
2025-09-11 15:02:10,839 - INFO - Load pretrained SentenceTransformer: shibing624/text2vec-base-chinese
2025-09-11 15:02:15,154 - INFO - 模型加载成功
2025-09-11 15:02:15,154 - INFO - 开始构建向量索引...
2025-09-11 15:02:15,154 - INFO - 正在处理批次 1/2190 (案例 1-4)
